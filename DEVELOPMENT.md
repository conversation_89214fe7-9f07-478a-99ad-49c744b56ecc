# 开发指南

## 环境准备

### 必需软件
1. **JDK 8+** - Java开发环境
   - 下载地址: https://www.oracle.com/java/technologies/downloads/
   - 配置JAVA_HOME环境变量

2. **Maven 3.6+** - 项目构建工具
   - 下载地址: https://maven.apache.org/download.cgi
   - 配置MAVEN_HOME环境变量

3. **MySQL 8.0+** - 数据库
   - 下载地址: https://dev.mysql.com/downloads/mysql/
   - 确保服务已启动

### 开发工具推荐
- **IDE**: IntelliJ IDEA / Eclipse
- **数据库工具**: Navicat / DBeaver
- **API测试**: Postman / Apifox

## 快速启动

### 1. 环境检查
运行环境检查脚本：
```bash
setup.bat
```

### 2. 数据库初始化
```sql
-- 1. 创建数据库
CREATE DATABASE shangpin_admin;

-- 2. 执行初始化脚本
-- 运行 src/main/resources/sql/init.sql
```

### 3. 配置修改
修改 `src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ******************************************
    username: your_username
    password: your_password
```

### 4. 启动项目
```bash
# 方式1: 使用启动脚本
start.bat

# 方式2: 使用Maven命令
mvn spring-boot:run

# 方式3: 在IDE中运行
# 运行 AdminSystemApplication.main() 方法
```

### 5. 验证启动
- 应用地址: http://localhost:8080/api
- API文档: http://localhost:8080/api/swagger-ui/
- 健康检查: http://localhost:8080/api/actuator/health

## 开发规范

### 代码结构
```
src/main/java/com/shangpin/admin/
├── common/          # 通用类（Result、常量等）
├── config/          # 配置类
├── controller/      # 控制器层
├── dto/            # 数据传输对象
├── entity/         # 实体类
├── exception/      # 异常处理
├── mapper/         # 数据访问层
├── service/        # 业务逻辑层
│   └── impl/       # 业务实现类
└── utils/          # 工具类
```

### 命名规范
- **类名**: 大驼峰命名法 (PascalCase)
- **方法名**: 小驼峰命名法 (camelCase)
- **常量**: 全大写下划线分隔 (UPPER_SNAKE_CASE)
- **包名**: 全小写 (lowercase)

### 注释规范
```java
/**
 * 类描述
 * 
 * <AUTHOR>
 * @since 版本
 */
public class Example {
    
    /**
     * 方法描述
     * 
     * @param param 参数描述
     * @return 返回值描述
     */
    public String method(String param) {
        return param;
    }
}
```

## API开发

### 控制器开发
```java
@RestController
@RequestMapping("/api/example")
@RequiredArgsConstructor
@Api(tags = "示例管理")
public class ExampleController {
    
    @GetMapping
    @ApiOperation("查询列表")
    public Result<List<Example>> list() {
        // 业务逻辑
        return Result.success(data);
    }
}
```

### 返回结果统一格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 异常处理
```java
// 抛出业务异常
throw new BusinessException(ResultCode.USER_NOT_EXIST);

// 自定义异常消息
throw new BusinessException("自定义错误消息");
```

## 数据库开发

### 实体类规范
```java
@Data
@TableName("table_name")
public class Entity {
    
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableLogic
    private Integer deleted;
}
```

### 查询构建
```java
LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(User::getStatus, 1)
           .like(User::getUsername, keyword)
           .orderByDesc(User::getCreateTime);
```

## 测试

### 单元测试
```java
@SpringBootTest
class ServiceTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    void testMethod() {
        // 测试逻辑
    }
}
```

### API测试
使用Swagger UI或Postman测试API接口

## 部署

### 打包
```bash
mvn clean package
```

### 运行
```bash
java -jar target/admin-system-1.0.0.jar
```

## 常见问题

### 1. 启动失败
- 检查Java环境变量
- 检查数据库连接配置
- 检查端口是否被占用

### 2. 数据库连接失败
- 确认MySQL服务已启动
- 检查数据库用户名密码
- 确认数据库已创建

### 3. 编译失败
- 检查Maven配置
- 清理并重新编译: `mvn clean compile`

## 扩展开发

### 添加新模块
1. 创建实体类 (entity)
2. 创建数据访问层 (mapper)
3. 创建业务逻辑层 (service)
4. 创建控制器 (controller)
5. 添加数据库表结构

### 集成第三方服务
- 文件上传: 阿里云OSS、七牛云
- 短信服务: 阿里云SMS、腾讯云SMS
- 支付服务: 支付宝、微信支付

## 技术支持

如有问题，请联系：
- 邮箱: <EMAIL>
- 文档: 查看README.md
