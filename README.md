# 尚品甑选后台管理系统

## 项目简介

尚品甑选后台管理系统是一个基于Spring Boot的现代化后台管理系统，提供用户管理、商品管理、订单管理等核心功能。

## 技术栈

- **后端框架**: Spring Boot 2.7.14
- **数据库**: MySQL 8.0+
- **ORM框架**: MyBatis Plus 3.5.3.1
- **安全框架**: Spring Security + JWT
- **API文档**: Swagger 3.0
- **工具库**: Hutool, Lombok
- **构建工具**: Maven

## 功能特性

### 已实现功能
- ✅ 用户认证与授权（JWT）
- ✅ 用户管理
- ✅ 角色管理
- ✅ 商品管理基础框架
- ✅ 统一返回结果封装
- ✅ 全局异常处理
- ✅ API接口文档
- ✅ 数据库自动填充

### 计划功能
- 🔄 商品分类管理
- 🔄 品牌管理
- 🔄 订单管理
- 🔄 库存管理
- 🔄 数据统计
- 🔄 系统日志
- 🔄 文件上传

## 快速开始

### 环境要求
- JDK 8+
- MySQL 8.0+
- Maven 3.6+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd shangpin-admin-system
   ```

2. **创建数据库**
   ```sql
   -- 执行 src/main/resources/sql/init.sql 文件
   ```

3. **修改配置**
   ```yaml
   # 修改 src/main/resources/application.yml 中的数据库配置
   spring:
     datasource:
       url: ******************************************
       username: your_username
       password: your_password
   ```

4. **启动项目**
   ```bash
   mvn spring-boot:run
   ```

5. **访问系统**
   - 应用地址: http://localhost:8080/api
   - API文档: http://localhost:8080/api/swagger-ui/

## 默认账号

| 用户名 | 密码 | 角色 |
|--------|------|------|
| admin | 123456 | 超级管理员 |
| operator | 123456 | 运营人员 |

## API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/userinfo` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

## 项目结构

```
src/
├── main/
│   ├── java/com/shangpin/admin/
│   │   ├── common/          # 通用类
│   │   ├── config/          # 配置类
│   │   ├── controller/      # 控制器
│   │   ├── dto/            # 数据传输对象
│   │   ├── entity/         # 实体类
│   │   ├── exception/      # 异常处理
│   │   ├── mapper/         # 数据访问层
│   │   ├── service/        # 业务逻辑层
│   │   └── utils/          # 工具类
│   └── resources/
│       ├── sql/            # 数据库脚本
│       └── application.yml # 配置文件
└── test/                   # 测试代码
```

## 开发规范

### 代码规范
- 使用Lombok减少样板代码
- 统一使用Result类封装返回结果
- 使用BusinessException处理业务异常
- 接口使用Swagger注解生成文档

### 数据库规范
- 表名使用下划线命名
- 字段名使用下划线命名
- 必须包含create_time、update_time字段
- 软删除使用deleted字段

### 接口规范
- RESTful API设计
- 统一返回格式
- 统一异常处理
- JWT认证授权

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: shangpin
- 邮箱: <EMAIL>
