@echo off
echo ========================================
echo 尚品甑选后台管理系统环境检查
echo ========================================

echo.
echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Java环境，请先安装JDK 8或更高版本
    echo 下载地址: https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Java环境检查通过
    java -version
)

echo.
echo 正在检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Maven环境，请先安装Maven 3.6或更高版本
    echo 下载地址: https://maven.apache.org/download.cgi
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Maven环境检查通过
    mvn -version
)

echo.
echo 正在检查MySQL环境...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 未检测到MySQL环境，请确保MySQL 8.0已安装并启动
    echo 下载地址: https://dev.mysql.com/downloads/mysql/
) else (
    echo [成功] MySQL环境检查通过
    mysql --version
)

echo.
echo ========================================
echo 环境检查完成！
echo ========================================
echo.
echo 接下来的步骤：
echo 1. 确保MySQL服务已启动
echo 2. 执行 src/main/resources/sql/init.sql 初始化数据库
echo 3. 修改 src/main/resources/application.yml 中的数据库配置
echo 4. 运行 mvn spring-boot:run 启动项目
echo 5. 访问 http://localhost:8080/api/swagger-ui/ 查看API文档
echo.
pause
