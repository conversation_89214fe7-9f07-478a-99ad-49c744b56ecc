package com.shangpin.admin.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回状态码枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    
    // 参数相关
    PARAM_ERROR(400, "参数错误"),
    PARAM_MISSING(400, "参数缺失"),
    PARAM_INVALID(400, "参数无效"),
    
    // 认证相关
    UNAUTHORIZED(401, "未认证"),
    TOKEN_INVALID(401, "Token无效"),
    TOKEN_EXPIRED(401, "Token已过期"),
    LOGIN_FAILED(401, "登录失败"),
    
    // 权限相关
    FORBIDDEN(403, "无权限访问"),
    ACCESS_DENIED(403, "访问被拒绝"),
    
    // 资源相关
    NOT_FOUND(404, "资源不存在"),
    RESOURCE_NOT_FOUND(404, "资源未找到"),
    
    // 业务相关
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_ALREADY_EXIST(1002, "用户已存在"),
    PASSWORD_ERROR(1003, "密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    
    PRODUCT_NOT_EXIST(2001, "商品不存在"),
    PRODUCT_STOCK_INSUFFICIENT(2002, "商品库存不足"),
    
    ORDER_NOT_EXIST(3001, "订单不存在"),
    ORDER_STATUS_ERROR(3002, "订单状态错误"),
    
    // 系统相关
    SYSTEM_ERROR(9999, "系统异常");
    
    private final Integer code;
    private final String message;
}
