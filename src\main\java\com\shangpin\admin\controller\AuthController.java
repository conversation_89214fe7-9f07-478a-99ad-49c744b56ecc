package com.shangpin.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.shangpin.admin.common.Result;
import com.shangpin.admin.dto.LoginRequest;
import com.shangpin.admin.dto.LoginResponse;
import com.shangpin.admin.entity.User;
import com.shangpin.admin.service.UserService;
import com.shangpin.admin.utils.JwtUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Api(tags = "认证管理")
public class AuthController {
    
    private final UserService userService;
    private final JwtUtils jwtUtils;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, 
                                     HttpServletRequest httpRequest) {
        // 执行登录
        String token = userService.login(request.getUsername(), request.getPassword());
        
        // 获取用户信息
        User user = userService.getByUsername(request.getUsername());
        
        // 更新登录信息
        String clientIp = getClientIp(httpRequest);
        userService.updateLastLoginInfo(user.getId(), clientIp);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(token);
        
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        BeanUtil.copyProperties(user, userInfo);
        response.setUserInfo(userInfo);
        
        return Result.success("登录成功", response);
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    @ApiOperation("获取当前用户信息")
    public Result<LoginResponse.UserInfo> getUserInfo(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        String username = jwtUtils.getUsernameFromToken(token);
        
        User user = userService.getByUsername(username);
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        BeanUtil.copyProperties(user, userInfo);
        
        return Result.success(userInfo);
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public Result<Void> logout() {
        // 这里可以实现Token黑名单机制
        return Result.success("登出成功");
    }
    
    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotEmpty(xfor) && !"unKnown".equalsIgnoreCase(xfor)) {
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        if (StrUtil.isNotEmpty(xip) && !"unKnown".equalsIgnoreCase(xip)) {
            return xip;
        }
        return request.getRemoteAddr();
    }
}
