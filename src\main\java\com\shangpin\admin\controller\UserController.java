package com.shangpin.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shangpin.admin.common.Result;
import com.shangpin.admin.entity.User;
import com.shangpin.admin.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Api(tags = "用户管理")
public class UserController {
    
    private final UserService userService;
    
    /**
     * 分页查询用户列表
     */
    @GetMapping
    @ApiOperation("分页查询用户列表")
    public Result<IPage<User>> getUserList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户名") @RequestParam(required = false) String username,
            @ApiParam("真实姓名") @RequestParam(required = false) String realName,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        
        Page<User> page = new Page<>(current, size);
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(username)) {
            queryWrapper.like(User::getUsername, username);
        }
        if (StringUtils.hasText(realName)) {
            queryWrapper.like(User::getRealName, realName);
        }
        if (status != null) {
            queryWrapper.eq(User::getStatus, status);
        }
        
        // 排序
        queryWrapper.orderByDesc(User::getCreateTime);
        
        IPage<User> result = userService.page(page, queryWrapper);
        
        // 清空密码字段
        result.getRecords().forEach(user -> user.setPassword(null));
        
        return Result.success(result);
    }
    
    /**
     * 根据ID查询用户详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询用户详情")
    public Result<User> getUserById(@ApiParam("用户ID") @PathVariable Long id) {
        User user = userService.getById(id);
        if (user != null) {
            user.setPassword(null); // 清空密码
        }
        return Result.success(user);
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    @ApiOperation("创建用户")
    public Result<Void> createUser(@RequestBody User user) {
        // 这里应该对密码进行加密
        boolean success = userService.save(user);
        return success ? Result.success("创建成功") : Result.error("创建失败");
    }
    
    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @ApiOperation("更新用户")
    public Result<Void> updateUser(@ApiParam("用户ID") @PathVariable Long id, 
                                  @RequestBody User user) {
        user.setId(id);
        boolean success = userService.updateById(user);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除用户")
    public Result<Void> deleteUser(@ApiParam("用户ID") @PathVariable Long id) {
        boolean success = userService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }
    
    /**
     * 启用/禁用用户
     */
    @PutMapping("/{id}/status")
    @ApiOperation("启用/禁用用户")
    public Result<Void> updateUserStatus(@ApiParam("用户ID") @PathVariable Long id,
                                        @ApiParam("状态") @RequestParam Integer status) {
        User user = new User();
        user.setId(id);
        user.setStatus(status);
        boolean success = userService.updateById(user);
        return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
    }
}
