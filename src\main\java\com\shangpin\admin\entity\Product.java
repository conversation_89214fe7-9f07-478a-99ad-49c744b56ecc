package com.shangpin.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("product")
public class Product {
    
    /**
     * 商品ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品编码
     */
    private String productCode;
    
    /**
     * 商品分类ID
     */
    private Long categoryId;
    
    /**
     * 商品分类名称
     */
    @TableField(exist = false)
    private String categoryName;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 品牌名称
     */
    @TableField(exist = false)
    private String brandName;
    
    /**
     * 商品主图
     */
    private String mainImage;
    
    /**
     * 商品图片（多张，逗号分隔）
     */
    private String images;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 商品详情
     */
    private String detail;
    
    /**
     * 商品价格
     */
    private BigDecimal price;
    
    /**
     * 市场价格
     */
    private BigDecimal marketPrice;
    
    /**
     * 成本价格
     */
    private BigDecimal costPrice;
    
    /**
     * 库存数量
     */
    private Integer stock;
    
    /**
     * 预警库存
     */
    private Integer warningStock;
    
    /**
     * 重量（克）
     */
    private Integer weight;
    
    /**
     * 体积（立方厘米）
     */
    private Integer volume;
    
    /**
     * 商品状态：0-下架，1-上架
     */
    private Integer status;
    
    /**
     * 是否推荐：0-否，1-是
     */
    private Integer isRecommend;
    
    /**
     * 是否热销：0-否，1-是
     */
    private Integer isHot;
    
    /**
     * 是否新品：0-否，1-是
     */
    private Integer isNew;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 销量
     */
    private Integer sales;
    
    /**
     * 浏览量
     */
    private Integer views;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
}
