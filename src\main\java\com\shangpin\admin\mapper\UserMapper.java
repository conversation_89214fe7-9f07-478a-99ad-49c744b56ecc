package com.shangpin.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shangpin.admin.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户（包含角色信息）
     */
    @Select("SELECT u.*, r.role_name FROM sys_user u " +
            "LEFT JOIN sys_role r ON u.role_id = r.id " +
            "WHERE u.username = #{username} AND u.deleted = 0")
    User selectByUsernameWithRole(@Param("username") String username);
}
