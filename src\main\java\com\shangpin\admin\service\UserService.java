package com.shangpin.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shangpin.admin.entity.User;

/**
 * 用户Service接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface UserService extends IService<User> {
    
    /**
     * 根据用户名查询用户
     */
    User getByUsername(String username);
    
    /**
     * 用户登录
     */
    String login(String username, String password);
    
    /**
     * 更新最后登录信息
     */
    void updateLastLoginInfo(Long userId, String loginIp);
}
