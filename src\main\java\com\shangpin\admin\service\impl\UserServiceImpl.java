package com.shangpin.admin.service.impl;

import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shangpin.admin.common.ResultCode;
import com.shangpin.admin.entity.User;
import com.shangpin.admin.exception.BusinessException;
import com.shangpin.admin.mapper.UserMapper;
import com.shangpin.admin.service.UserService;
import com.shangpin.admin.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户Service实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    private final JwtUtils jwtUtils;
    
    @Override
    public User getByUsername(String username) {
        return baseMapper.selectByUsernameWithRole(username);
    }
    
    @Override
    public String login(String username, String password) {
        // 查询用户
        User user = getByUsername(username);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }
        
        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new BusinessException(ResultCode.USER_DISABLED);
        }
        
        // 验证密码
        if (!BCrypt.checkpw(password, user.getPassword())) {
            throw new BusinessException(ResultCode.PASSWORD_ERROR);
        }
        
        // 生成Token
        String token = jwtUtils.generateToken(user.getId(), user.getUsername());
        
        log.info("用户登录成功: {}", username);
        return token;
    }
    
    @Override
    public void updateLastLoginInfo(Long userId, String loginIp) {
        User user = new User();
        user.setId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(loginIp);
        updateById(user);
    }
}
