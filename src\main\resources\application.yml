server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: shangpin-admin-system
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************
    username: root
    password: 123456
    
  # <PERSON>配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.shangpin.admin.entity
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: shangpinAdminSystemSecretKey2024
  expiration: 86400000  # 24小时，单位毫秒

# 日志配置
logging:
  level:
    com.shangpin.admin: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
