-- 创建数据库
CREATE DATABASE IF NOT EXISTS `shangpin_admin` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `shangpin_admin`;

-- 系统用户表
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint DEFAULT '1' COMMENT '性别：0-女，1-男',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `role_id` bigint DEFAULT NULL COMMENT '角色ID',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 系统角色表
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort` int DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统角色表';

-- 商品表
CREATE TABLE `product` (
  `id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_code` varchar(50) DEFAULT NULL COMMENT '商品编码',
  `category_id` bigint DEFAULT NULL COMMENT '商品分类ID',
  `brand_id` bigint DEFAULT NULL COMMENT '品牌ID',
  `main_image` varchar(500) DEFAULT NULL COMMENT '商品主图',
  `images` text COMMENT '商品图片（多张，逗号分隔）',
  `description` varchar(500) DEFAULT NULL COMMENT '商品描述',
  `detail` text COMMENT '商品详情',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价格',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价格',
  `stock` int DEFAULT '0' COMMENT '库存数量',
  `warning_stock` int DEFAULT '0' COMMENT '预警库存',
  `weight` int DEFAULT NULL COMMENT '重量（克）',
  `volume` int DEFAULT NULL COMMENT '体积（立方厘米）',
  `status` tinyint DEFAULT '1' COMMENT '商品状态：0-下架，1-上架',
  `is_recommend` tinyint DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `is_hot` tinyint DEFAULT '0' COMMENT '是否热销：0-否，1-是',
  `is_new` tinyint DEFAULT '0' COMMENT '是否新品：0-否，1-是',
  `sort` int DEFAULT '0' COMMENT '排序',
  `sales` int DEFAULT '0' COMMENT '销量',
  `views` int DEFAULT '0' COMMENT '浏览量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_brand_id` (`brand_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 插入初始数据
-- 插入角色数据
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `status`, `sort`) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1),
(2, '管理员', 'ADMIN', '系统管理员', 1, 2),
(3, '运营人员', 'OPERATOR', '运营人员', 1, 3);

-- 插入用户数据（密码为123456的BCrypt加密结果）
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `email`, `phone`, `gender`, `status`, `role_id`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlQDQmM6oCE.3nDVgs6XjuLqAHa', '超级管理员', '<EMAIL>', '13800138000', 1, 1, 1),
(2, 'operator', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlQDQmM6oCE.3nDVgs6XjuLqAHa', '运营人员', '<EMAIL>', '13800138001', 1, 1, 3);
